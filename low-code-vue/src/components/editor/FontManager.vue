<template>
  <el-dialog
    v-model="visible"
    title="字体管理"
    width="800px"
    :before-close="handleClose"
  >
    <div class="font-manager">
      <!-- 工具栏 -->
      <div class="toolbar">
        <el-button type="primary" icon="Plus" @click="showAddFontDialog">
          添加字体
        </el-button>
        <el-button icon="Refresh" @click="refreshFonts">
          刷新
        </el-button>
      </div>

      <!-- 字体列表 -->
      <div class="font-list">
        <el-table :data="fontList" style="width: 100%" max-height="400">
          <el-table-column prop="name" label="字体名称" width="150">
            <template #default="{ row }">
              <span :style="{ fontFamily: row.family }">{{ row.name }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="family" label="字体族" width="200" show-overflow-tooltip />
          
          <el-table-column prop="type" label="类型" width="80">
            <template #default="{ row }">
              <el-tag 
                :type="getTypeTagType(row.type)" 
                size="small"
              >
                {{ getTypeLabel(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="预览" width="100">
            <template #default="{ row }">
              <span 
                class="font-preview-text" 
                :style="{ fontFamily: row.family }"
              >
                {{ row.preview }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="80">
            <template #default="{ row }">
              <el-tag
                v-if="row.type === 'web' || row.type === 'custom'"
                :type="isLoaded(row.id) ? 'success' : 'info'"
                size="small"
              >
                {{ isLoaded(row.id) ? '已加载' : '未加载' }}
              </el-tag>
              <el-tag v-else type="success" size="small">可用</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button
                v-if="(row.type === 'web' || row.type === 'custom') && !isLoaded(row.id)"
                size="small"
                type="primary"
                @click="loadFont(row)"
              >
                加载
              </el-button>
              <el-button
                v-if="row.type === 'custom'"
                size="small"
                @click="editFont(row)"
              >
                编辑
              </el-button>
              <el-button
                v-if="row.type === 'custom'"
                size="small"
                type="danger"
                @click="deleteFont(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 添加/编辑字体对话框 -->
    <el-dialog
      v-model="fontDialogVisible"
      :title="editingFont ? '编辑字体' : '添加字体'"
      width="500px"
      append-to-body
    >
      <el-form :model="fontForm" :rules="fontRules" ref="fontFormRef" label-width="80px">
        <el-form-item label="字体名称" prop="name">
          <el-input v-model="fontForm.name" placeholder="请输入字体名称" />
        </el-form-item>
        
        <el-form-item label="字体族" prop="family">
          <el-input 
            v-model="fontForm.family" 
            placeholder="例如: 'Arial', sans-serif"
            type="textarea"
            :rows="2"
          />
        </el-form-item>
        
        <el-form-item label="字体类型" prop="type">
          <el-radio-group v-model="fontForm.type">
            <el-radio label="web">Web字体</el-radio>
            <el-radio label="custom">自定义字体</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item
          v-if="fontForm.type === 'web'"
          label="字体URL"
          prop="url"
        >
          <el-input
            v-model="fontForm.url"
            placeholder="请输入字体CSS文件URL"
            type="textarea"
            :rows="2"
          />
        </el-form-item>

        <el-form-item
          v-if="fontForm.type === 'custom'"
          label="字体文件"
          prop="fontFile"
        >
          <el-upload
            ref="fontUploadRef"
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            accept=".ttf,.otf,.woff,.woff2"
            :on-change="handleFontFileChange"
            :on-remove="handleFontFileRemove"
            drag
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将字体文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 .ttf, .otf, .woff, .woff2 格式的字体文件，文件大小不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="预览文本" prop="preview">
          <el-input v-model="fontForm.preview" placeholder="Aa 或 字体" />
        </el-form-item>
        
        <el-form-item label="预览">
          <div 
            class="font-preview-demo"
            :style="{ fontFamily: fontForm.family }"
          >
            {{ fontForm.preview || 'Aa' }}
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="fontDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveFontForm">确定</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { useEditorStore } from '../../store'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { imageApi, IMAGE_CATEGORIES } from '../../api/image'

// 调试信息
console.log('FontManager.vue - imageApi imported:', imageApi)
console.log('FontManager.vue - IMAGE_CATEGORIES imported:', IMAGE_CATEGORIES)

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const editorStore = useEditorStore()

// 对话框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 字体列表
const fontList = computed(() => editorStore.fontLibrary)

// 字体对话框
const fontDialogVisible = ref(false)
const editingFont = ref(null)
const fontFormRef = ref()
const fontUploadRef = ref()

// 字体表单
const fontForm = ref({
  name: '',
  family: '',
  type: 'web',
  url: '',
  preview: 'Aa',
  fontFile: null,
  fontUrl: ''
})

// 表单验证规则
const fontRules = {
  name: [
    { required: true, message: '请输入字体名称', trigger: 'blur' }
  ],
  family: [
    { required: true, message: '请输入字体族', trigger: 'blur' }
  ],
  url: [
    { 
      required: true, 
      message: '请输入字体URL', 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (fontForm.value.type === 'web' && !value) {
          callback(new Error('Web字体必须提供URL'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 获取类型标签类型
const getTypeTagType = (type) => {
  switch (type) {
    case 'system': return 'success'
    case 'web': return 'primary'
    case 'custom': return 'warning'
    default: return 'info'
  }
}

// 获取类型标签文本
const getTypeLabel = (type) => {
  switch (type) {
    case 'system': return '系统'
    case 'web': return 'Web'
    case 'custom': return '自定义'
    default: return '未知'
  }
}

// 检查字体是否已加载
const isLoaded = (fontId) => {
  return editorStore.loadedFonts.has(fontId)
}

// 加载字体
const loadFont = async (font) => {
  try {
    const loaded = await editorStore.loadWebFont(font)
    if (loaded) {
      ElMessage.success(`字体 ${font.name} 加载成功`)
    } else {
      ElMessage.error(`字体 ${font.name} 加载失败`)
    }
  } catch (error) {
    console.error('Error loading font:', error)
    ElMessage.error('字体加载失败')
  }
}

// 处理字体文件变化
const handleFontFileChange = (file) => {
  console.log('Font file changed:', file)
  fontForm.value.fontFile = file.raw

  // 自动设置字体名称（如果为空）
  if (!fontForm.value.name && file.name) {
    const nameWithoutExt = file.name.replace(/\.[^/.]+$/, '')
    fontForm.value.name = nameWithoutExt
  }

  // 自动设置字体族（如果为空）
  if (!fontForm.value.family && file.name) {
    const nameWithoutExt = file.name.replace(/\.[^/.]+$/, '')
    fontForm.value.family = `"${nameWithoutExt}", sans-serif`
  }
}

// 处理字体文件移除
const handleFontFileRemove = () => {
  fontForm.value.fontFile = null
}

// 显示添加字体对话框
const showAddFontDialog = () => {
  editingFont.value = null
  fontForm.value = {
    name: '',
    family: '',
    type: 'web',
    url: '',
    preview: 'Aa',
    fontFile: null,
    fontUrl: ''
  }
  fontDialogVisible.value = true

  // 清空上传组件
  nextTick(() => {
    if (fontUploadRef.value) {
      fontUploadRef.value.clearFiles()
    }
  })
}

// 编辑字体
const editFont = (font) => {
  editingFont.value = font
  fontForm.value = { ...font }
  fontDialogVisible.value = true
}

// 删除字体
const deleteFont = async (font) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除字体 "${font.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    editorStore.removeFont(font.id)
    ElMessage.success('字体删除成功')
  } catch {
    // 用户取消删除
  }
}

// 保存字体表单
const saveFontForm = async () => {
  try {
    await fontFormRef.value.validate()

    let fontData = { ...fontForm.value }

    // 如果是自定义字体且有文件，先上传文件
    if (fontForm.value.type === 'custom' && fontForm.value.fontFile) {
      try {
        ElMessage.info('正在上传字体文件...')

        // 上传字体文件
        const uploadResult = await imageApi.uploadImage(
          fontForm.value.fontFile,
          IMAGE_CATEGORIES.FONT_FILE, // 字体文件分类
          'font',
          null,
          `字体文件: ${fontForm.value.name}`
        )

        if (uploadResult && uploadResult.url) {
          fontData.fontUrl = uploadResult.url
          fontData.fileId = uploadResult.id
          ElMessage.success('字体文件上传成功')
        } else {
          throw new Error('文件上传失败')
        }
      } catch (uploadError) {
        console.error('Font file upload failed:', uploadError)
        ElMessage.error('字体文件上传失败: ' + (uploadError.message || '未知错误'))
        return
      }
    }

    if (editingFont.value) {
      // 编辑现有字体
      editorStore.updateFont(editingFont.value.id, fontData)
      ElMessage.success('字体更新成功')
    } else {
      // 添加新字体
      editorStore.addFont(fontData)
      ElMessage.success('字体添加成功')
    }

    fontDialogVisible.value = false
  } catch (error) {
    console.error('Form validation failed:', error)
  }
}

// 刷新字体
const refreshFonts = () => {
  ElMessage.success('字体列表已刷新')
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.font-manager {
  padding: 10px 0;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.font-list {
  margin-bottom: 20px;
}

.font-preview-text {
  font-size: 16px;
  font-weight: 500;
}

.font-preview-demo {
  font-size: 24px;
  font-weight: 500;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  text-align: center;
  min-height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
