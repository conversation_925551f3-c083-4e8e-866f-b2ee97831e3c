import { request } from './api'

/**
 * 图片管理API
 */

// 上传单个图片
export function uploadImage(file, category = 2, businessType = null, businessId = null, remark = null) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('category', category)
  if (businessType) formData.append('businessType', businessType)
  if (businessId) formData.append('businessId', businessId)
  if (remark) formData.append('remark', remark)

  return request({
    url: '/images/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 上传单个图片（原始方法，保持兼容性）
export function uploadImageFormData(formData) {
  return request({
    url: '/images/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 批量上传图片
export function uploadImages(formData) {
  return request({
    url: '/images/upload/batch',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 分页查询图片列表
export function getImagePage(params) {
  return request({
    url: '/images/page',
    method: 'get',
    params
  })
}

// 根据ID获取图片详情
export function getImageById(id) {
  return request({
    url: `/images/${id}`,
    method: 'get'
  })
}

// 根据业务信息查询图片列表
export function getImagesByBusiness(businessType, businessId) {
  return request({
    url: '/images/business',
    method: 'get',
    params: {
      businessType,
      businessId
    }
  })
}

// 更新图片信息
export function updateImage(id, data) {
  return request({
    url: `/images/${id}`,
    method: 'put',
    data
  })
}

// 删除图片（软删除）
export function deleteImage(id) {
  return request({
    url: `/images/${id}`,
    method: 'delete'
  })
}

// 批量删除图片
export function deleteImages(ids) {
  return request({
    url: '/images/batch',
    method: 'delete',
    data: ids
  })
}

// 恢复已删除的图片
export function restoreImage(id) {
  return request({
    url: `/images/${id}/restore`,
    method: 'put'
  })
}

// 永久删除图片
export function permanentDeleteImage(id) {
  return request({
    url: `/images/${id}/permanent`,
    method: 'delete'
  })
}

// 复制图片
export function copyImage(id, params) {
  return request({
    url: `/images/${id}/copy`,
    method: 'post',
    params
  })
}

// 下载图片
export function downloadImage(id) {
  return request({
    url: `/images/${id}/download`,
    method: 'get',
    responseType: 'blob'
  })
}

// 获取图片统计信息
export function getImageStatistics() {
  return request({
    url: '/images/statistics',
    method: 'get'
  })
}

// 获取分类统计信息
export function getCategoryStatistics(category) {
  return request({
    url: `/images/statistics/category/${category}`,
    method: 'get'
  })
}

// 图片操作日志相关API

// 分页查询操作日志
export function getImageLogPage(params) {
  return request({
    url: '/image-logs/page',
    method: 'get',
    params
  })
}

// 根据图片ID查询操作日志
export function getLogsByImageId(imageId) {
  return request({
    url: `/image-logs/image/${imageId}`,
    method: 'get'
  })
}

// 统计操作次数
export function countOperations(operationType, startTime, endTime) {
  return request({
    url: '/image-logs/count',
    method: 'get',
    params: {
      operationType,
      startTime,
      endTime
    }
  })
}

// 获取操作统计信息
export function getOperationStatistics(startTime, endTime) {
  return request({
    url: '/image-logs/statistics',
    method: 'get',
    params: {
      startTime,
      endTime
    }
  })
}

// 清理过期日志
export function cleanExpiredLogs(days = 90) {
  return request({
    url: '/image-logs/clean',
    method: 'delete',
    params: {
      days
    }
  })
}

// 图片分类常量
export const IMAGE_CATEGORIES = {
  COMPONENT_ICON: 1,    // 组件图标
  USER_UPLOAD: 2,       // 用户上传
  SYSTEM_IMAGE: 3,      // 系统图片
  TEMPLATE_THUMBNAIL: 4, // 模板缩略图
  FONT_FILE: 5          // 字体文件
}

// 图片分类名称映射
export const CATEGORY_NAMES = {
  [IMAGE_CATEGORIES.COMPONENT_ICON]: '组件图标',
  [IMAGE_CATEGORIES.USER_UPLOAD]: '用户上传',
  [IMAGE_CATEGORIES.SYSTEM_IMAGE]: '系统图片',
  [IMAGE_CATEGORIES.TEMPLATE_THUMBNAIL]: '模板缩略图',
  [IMAGE_CATEGORIES.FONT_FILE]: '字体文件'
}

// 业务类型常量
export const BUSINESS_TYPES = {
  COMPONENT: 'component',
  PROJECT: 'project',
  TEMPLATE: 'template',
  USER: 'user'
}

// 操作类型常量
export const OPERATION_TYPES = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  VIEW: 'VIEW',
  DOWNLOAD: 'DOWNLOAD'
}

// 图片状态常量
export const IMAGE_STATUS = {
  NORMAL: 0,    // 正常
  DELETED: 1    // 已删除
}
