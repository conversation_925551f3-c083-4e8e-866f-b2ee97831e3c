# 字体管理导入错误修复指南

## 🚨 错误信息

```
SyntaxError: The requested module '/src/api/image.js?t=1748800146584' does not provide an export named 'imageApi' (at FontManager.vue:191:10)
```

## 🔍 问题分析

这个错误表明 `image.js` 模块没有正确导出 `imageApi` 对象。可能的原因：

1. **浏览器缓存问题** - 浏览器缓存了旧版本的模块
2. **开发服务器缓存** - Vite开发服务器没有正确重新加载模块
3. **模块导出问题** - 导出语法可能有问题

## 🔧 解决方案

### 方案1: 清除缓存并重启开发服务器

1. **停止开发服务器** (Ctrl+C)

2. **清除浏览器缓存**:
   - 打开浏览器开发者工具 (F12)
   - 右键点击刷新按钮
   - 选择"清空缓存并硬性重新加载"

3. **清除项目缓存**:
   ```bash
   cd low-code-vue
   rm -rf node_modules/.vite
   rm -rf dist
   ```

4. **重启开发服务器**:
   ```bash
   npm run dev
   ```

### 方案2: 修改导入方式 (已实施)

我已经修改了 `FontManager.vue` 中的导入方式：

**修改前**:
```javascript
import { imageApi, IMAGE_CATEGORIES } from '../../api/image'
```

**修改后**:
```javascript
import * as imageModule from '../../api/image'
const { imageApi, IMAGE_CATEGORIES } = imageModule
```

这种方式可以避免ES模块导入的一些问题。

### 方案3: 验证模块导出

检查 `image.js` 文件末尾是否有正确的导出：

```javascript
// 统一导出API对象
export const imageApi = {
  uploadImage,
  uploadImageFormData,
  uploadImages,
  getImagePage,
  getImageById,
  getImagesByBusiness,
  updateImage,
  deleteImage,
  deleteImages,
  restoreImage,
  permanentDeleteImage,
  copyImage,
  downloadImage,
  getImageStatistics,
  getCategoryStatistics,
  getImageLogPage,
  getLogsByImageId,
  countOperations,
  getOperationStatistics,
  cleanExpiredLogs
}
```

## 🧪 测试步骤

1. **重启开发服务器**后访问页面
2. **打开浏览器控制台**查看调试信息：
   ```
   FontManager.vue - imageModule imported: [Object]
   FontManager.vue - imageApi: [Object]
   FontManager.vue - IMAGE_CATEGORIES: [Object]
   ```
3. **测试字体管理功能**：
   - 点击"字体管理"按钮
   - 尝试添加新字体
   - 上传字体文件

## 🔄 如果问题仍然存在

### 备用方案1: 使用api.js统一导入

```javascript
import { imageApi, IMAGE_CATEGORIES } from '../../api/api'
```

### 备用方案2: 直接导入函数

```javascript
import { uploadImage, IMAGE_CATEGORIES } from '../../api/image'

// 在使用时直接调用函数
const uploadResult = await uploadImage(
  fontForm.value.fontFile,
  IMAGE_CATEGORIES.FONT_FILE,
  'font',
  null,
  `字体文件: ${fontForm.value.name}`
)
```

### 备用方案3: 动态导入

```javascript
// 在需要使用时动态导入
const saveFontForm = async () => {
  try {
    await fontFormRef.value.validate()
    
    let fontData = { ...fontForm.value }
    
    if (fontForm.value.type === 'custom' && fontForm.value.fontFile) {
      try {
        ElMessage.info('正在上传字体文件...')
        
        // 动态导入
        const { uploadImage, IMAGE_CATEGORIES } = await import('../../api/image')
        
        const uploadResult = await uploadImage(
          fontForm.value.fontFile,
          IMAGE_CATEGORIES.FONT_FILE,
          'font',
          null,
          `字体文件: ${fontForm.value.name}`
        )
        
        // ... 其余代码
      } catch (uploadError) {
        console.error('Font file upload failed:', uploadError)
        ElMessage.error('字体文件上传失败: ' + (uploadError.message || '未知错误'))
        return
      }
    }
    
    // ... 其余代码
  } catch (error) {
    console.error('Form validation failed:', error)
  }
}
```

## 📝 注意事项

1. **模块热重载**: Vite的模块热重载有时可能不完全生效，需要手动刷新
2. **ES模块语法**: 确保使用正确的ES模块导入/导出语法
3. **路径问题**: 确保导入路径正确，特别是相对路径
4. **循环依赖**: 避免模块之间的循环依赖

## ✅ 验证修复

修复后应该能够：

1. ✅ 正常打开字体管理器
2. ✅ 添加新的自定义字体
3. ✅ 上传字体文件
4. ✅ 在控制台看到正确的调试信息
5. ✅ 没有模块导入错误

## 🎯 最终状态

修复完成后，字体管理功能应该能够：

- 📁 支持上传 .ttf, .otf, .woff, .woff2 格式的字体文件
- 🎨 自动生成字体名称和字体族
- 💾 将字体文件上传到服务器存储
- 🔄 动态加载和应用自定义字体
- 📊 管理字体库和加载状态

---

**修复状态**: ✅ 已实施方案2  
**测试状态**: ⏳ 待验证  
**影响范围**: 字体管理功能
